import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pymongo import MongoClient, ASCENDING
from pymongo.errors import DuplicateKeyError, ConnectionFailure
from bson import ObjectId
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# MongoDB connection from environment variables
MONGODB_URI = os.getenv("MONGODB_URI", "mongodb+srv://rehul123:<EMAIL>/")
DATABASE_NAME = os.getenv("MONGODB_DATABASE_NAME", "telegram_autopost_bot")

# Data directory for images (still needed for file storage)
DATA_DIR = "data"
IMAGE_DIR = os.path.join(DATA_DIR, "images")
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(IMAGE_DIR, exist_ok=True)

class DatabaseManager:
    """MongoDB database manager for the Telegram Auto-Posting Bot."""
    
    def __init__(self):
        self.client = None
        self.db = None
        self.connect()
    
    def connect(self):
        """Establish connection to MongoDB."""
        try:
            self.client = MongoClient(MONGODB_URI)
            self.db = self.client[DATABASE_NAME]
            # Test connection
            self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
            self.create_indexes()
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    def create_indexes(self):
        """Create necessary indexes for optimal performance."""
        try:
            # Users collection indexes
            self.db.users.create_index("user_id", unique=True)
            
            # Channels collection indexes
            self.db.channels.create_index([("user_id", ASCENDING), ("channel_id", ASCENDING)], unique=True)
            self.db.channels.create_index("user_id")
            self.db.channels.create_index("channel_id", unique=True)  # For channel ownership protection
            
            # Content types collection indexes
            self.db.content_types.create_index("type_id", unique=True)
            
            # Projects collection indexes
            self.db.projects.create_index([("user_id", ASCENDING), ("project_id", ASCENDING)], unique=True)
            self.db.projects.create_index("user_id")
            self.db.projects.create_index("active")
            
            # Post history collection indexes
            self.db.post_history.create_index([("user_id", ASCENDING), ("project_id", ASCENDING)])
            self.db.post_history.create_index("timestamp")
            
            logger.info("Database indexes created successfully")
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")

# Global database manager instance
db_manager = DatabaseManager()

def initialize_database():
    """Initialize the database collections and default content types."""
    try:
        # Initialize default content types if they don't exist
        default_content_types = [
            {
                "type_id": "daily_news_summary",
                "name": "Daily News Summary",
                "description": "Generates a daily summary of news for India",
                "parameters": {
                    "country": "India",
                    "post_interval_hours": 24,
                    "post_time_hour": 8,
                    "post_time_minute": 0,
                    "timezone_country": "India",
                    "last_posted": None
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "type_id": "crypto_prices",
                "name": "Crypto Prices",
                "description": "Posts cryptocurrency price updates",
                "parameters": {
                    "num_coins": 5,
                    "post_interval_hours": 24,
                    "post_time_hour": 9,
                    "post_time_minute": 0,
                    "timezone_country": "India",
                    "last_posted": None
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "type_id": "health_fitness",
                "name": "Health & Fitness",
                "description": "Posts health and fitness tips and information",
                "parameters": {
                    "post_interval_hours": 24,
                    "post_time_hour": 7,
                    "post_time_minute": 0,
                    "timezone_country": "India",
                    "last_posted": None
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "type_id": "cricket_news",
                "name": "Cricket News",
                "description": "Posts latest cricket news and updates",
                "parameters": {
                    "post_interval_hours": 24,
                    "post_time_hour": 18,
                    "post_time_minute": 0,
                    "timezone_country": "India",
                    "last_posted": None
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "type_id": "custom_content",
                "name": "Custom Content",
                "description": "Posts custom content using user-defined prompts",
                "parameters": {
                    "post_interval_hours": 24,
                    "post_time_hour": 12,
                    "post_time_minute": 0,
                    "timezone_country": "India",
                    "last_posted": None,
                    "custom_prompt": "Hello there, Tell me a fact. add a text below (Made with auto posting bot)"
                },
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
        ]
        
        for content_type in default_content_types:
            try:
                # Insert only if it doesn't exist
                existing = db_manager.db.content_types.find_one({"type_id": content_type["type_id"]})
                if not existing:
                    db_manager.db.content_types.insert_one(content_type)
                    logger.info(f"Inserted content type: {content_type['type_id']}")
            except DuplicateKeyError:
                # Content type already exists, skip
                pass
        
        # Initialize admin user if not exists
        admin_user_id = 1049516929
        admin_user = db_manager.db.users.find_one({"user_id": admin_user_id})

        if not admin_user:
            admin_doc = {
                "user_id": admin_user_id,
                "is_admin": True,
                "is_pro": True,
                "project_limit": 999,  # Unlimited for admin
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            db_manager.db.users.insert_one(admin_doc)
            logger.info(f"Created admin user {admin_user_id}")
        else:
            # Ensure admin has proper privileges
            db_manager.db.users.update_one(
                {"user_id": admin_user_id},
                {"$set": {
                    "is_admin": True,
                    "is_pro": True,
                    "project_limit": 999,
                    "updated_at": datetime.utcnow()
                }}
            )
            logger.info(f"Updated admin user {admin_user_id} privileges")

        logger.info("Database initialization completed")

    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

# ============================================================================
# CHANNEL MANAGEMENT FUNCTIONS
# ============================================================================

def get_channels(user_id: int = None) -> Dict[str, Dict[str, Any]]:
    """Get channels from the database for a specific user."""
    try:
        # If no user_id provided, return empty dict (for security)
        if user_id is None:
            return {}
        
        # Query channels for this user
        channels_cursor = db_manager.db.channels.find({"user_id": user_id})
        
        # Convert to the expected format (channel_id as key)
        channels = {}
        for channel_doc in channels_cursor:
            channel_id = channel_doc["channel_id"]
            # Convert MongoDB document to expected format
            channel_data = {
                "user_id": channel_doc["user_id"],
                "name": channel_doc["name"],
                "type": channel_doc["type"],
                "added_at": channel_doc["added_at"].isoformat() if isinstance(channel_doc["added_at"], datetime) else channel_doc["added_at"]
            }
            channels[channel_id] = channel_data
        
        return channels
    except Exception as e:
        logger.error(f"Error getting channels for user {user_id}: {e}")
        return {}

def check_channel_ownership(channel_id: str) -> tuple[bool, int]:
    """
    Check if a channel is already owned by any user.

    Args:
        channel_id (str): The channel ID to check

    Returns:
        tuple[bool, int]: (is_owned, owner_user_id)
                         If not owned, owner_user_id will be 0
    """
    try:
        existing_channel = db_manager.db.channels.find_one({"channel_id": channel_id})

        if existing_channel:
            return True, existing_channel["user_id"]
        else:
            return False, 0

    except Exception as e:
        logger.error(f"Error checking channel ownership for {channel_id}: {e}")
        return False, 0

def get_channel_ownership_info(channel_id: str) -> Dict[str, Any]:
    """
    Get detailed ownership information for a channel (admin function).

    Args:
        channel_id (str): The channel ID to check

    Returns:
        Dict[str, Any]: Channel ownership details or empty dict if not found
    """
    try:
        channel_doc = db_manager.db.channels.find_one({"channel_id": channel_id})

        if channel_doc:
            return {
                "channel_id": channel_doc["channel_id"],
                "owner_user_id": channel_doc["user_id"],
                "channel_name": channel_doc["name"],
                "channel_type": channel_doc["type"],
                "added_at": channel_doc["added_at"],
                "created_at": channel_doc["created_at"],
                "updated_at": channel_doc["updated_at"]
            }
        else:
            return {}

    except Exception as e:
        logger.error(f"Error getting channel ownership info for {channel_id}: {e}")
        return {}

def add_channel(user_id: int, channel_id: str, channel_name: str, channel_type: str) -> tuple[bool, str]:
    """
    Add a channel to the database for a specific user.

    Args:
        user_id (int): The user ID
        channel_id (str): The channel ID
        channel_name (str): The channel name
        channel_type (str): The channel type

    Returns:
        tuple[bool, str]: (success, message)
    """
    try:
        # Check if this user already has this channel
        existing_user_channel = db_manager.db.channels.find_one({
            "user_id": user_id,
            "channel_id": channel_id
        })

        if existing_user_channel:
            return False, f"Channel '{channel_name}' is already in your list."

        # CRITICAL SECURITY CHECK: Check if ANY other user owns this channel
        is_owned, owner_user_id = check_channel_ownership(channel_id)
        if is_owned and owner_user_id != user_id:
            logger.warning(f"User {user_id} attempted to add channel {channel_id} already owned by user {owner_user_id}")
            return False, "This channel is already being used by another user. Each channel can only be managed by one user at a time."

        # Create channel document
        channel_doc = {
            "channel_id": channel_id,
            "user_id": user_id,
            "name": channel_name,
            "type": channel_type,
            "added_at": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }

        # Insert the channel
        db_manager.db.channels.insert_one(channel_doc)
        logger.info(f"Added channel {channel_id} for user {user_id}")
        return True, f"Channel '{channel_name}' added successfully!\n\nYou can now create projects for this channel using /addprojects."

    except Exception as e:
        logger.error(f"Error adding channel {channel_id} for user {user_id}: {e}")
        return False, "An error occurred while adding the channel. Please try again."

def remove_channel(user_id: int, channel_id: str) -> bool:
    """
    Remove a channel from the database for a specific user.
    This also ensures proper cleanup of channel ownership.
    """
    try:
        # Verify the user owns this channel before deletion
        existing_channel = db_manager.db.channels.find_one({
            "user_id": user_id,
            "channel_id": channel_id
        })

        if not existing_channel:
            logger.warning(f"Channel {channel_id} not found for user {user_id}")
            return False

        # Delete the channel (this releases ownership)
        result = db_manager.db.channels.delete_one({
            "user_id": user_id,
            "channel_id": channel_id
        })

        if result.deleted_count > 0:
            logger.info(f"Removed channel {channel_id} for user {user_id} - ownership released")

            # Also remove any projects that use this channel
            projects_result = db_manager.db.projects.delete_many({
                "user_id": user_id,
                "channels.channel_id": channel_id
            })

            if projects_result.deleted_count > 0:
                logger.info(f"Removed {projects_result.deleted_count} projects using channel {channel_id}")

            return True
        else:
            return False

    except Exception as e:
        logger.error(f"Error removing channel {channel_id} for user {user_id}: {e}")
        return False

# ============================================================================
# PROJECT MANAGEMENT FUNCTIONS
# ============================================================================

def get_projects(user_id: int = None) -> Dict[str, Dict[str, Any]]:
    """Get projects from the database for a specific user."""
    try:
        # If no user_id provided, return empty dict (for security)
        if user_id is None:
            return {}

        # Query projects for this user
        projects_cursor = db_manager.db.projects.find({"user_id": user_id})

        # Convert to the expected format (project_id as key)
        projects = {}
        for project_doc in projects_cursor:
            project_id = project_doc["project_id"]
            # Convert MongoDB document to expected format
            project_data = {
                "user_id": project_doc["user_id"],
                "name": project_doc["name"],
                "channels": project_doc["channels"],
                "created_at": project_doc["created_at"].isoformat() if isinstance(project_doc["created_at"], datetime) else project_doc["created_at"],
                "active": project_doc.get("active", True),
                "content_settings": project_doc.get("content_settings", {}),
                "image_settings": project_doc.get("image_settings", {"mode": "default", "custom_image_path": None}),
                "button_settings": project_doc.get("button_settings", {"enabled": False, "buttons": []}),
                "custom_prompt": project_doc.get("custom_prompt")
            }
            projects[project_id] = project_data

        return projects
    except Exception as e:
        logger.error(f"Error getting projects for user {user_id}: {e}")
        return {}

def add_project(user_id: int, project_id: str, project_name: str, channels: List[Dict[str, str]]) -> tuple[bool, str]:
    """Add a project to the database for a specific user. Returns (success, message)."""
    try:
        # Check if user can create a new project
        can_create, reason = can_user_create_project(user_id)
        if not can_create:
            return False, reason

        # Check if this user already has this project
        existing_project = db_manager.db.projects.find_one({
            "user_id": user_id,
            "project_id": project_id
        })

        if existing_project:
            return False, "Project with this ID already exists"

        # Create project document
        project_doc = {
            "project_id": project_id,
            "user_id": user_id,
            "name": project_name,
            "channels": channels,
            "active": True,
            "content_settings": {},
            "image_settings": {"mode": "default", "custom_image_path": None},
            "button_settings": {"enabled": False, "buttons": []},
            "custom_prompt": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }

        # Insert the project
        db_manager.db.projects.insert_one(project_doc)

        # Get updated counts for success message
        current_count = get_user_project_count(user_id)
        project_limit = get_user_project_limit(user_id)

        logger.info(f"Added project {project_id} for user {user_id}")
        return True, f"Project created successfully ({current_count}/{project_limit})"

    except Exception as e:
        logger.error(f"Error adding project {project_id} for user {user_id}: {e}")
        return False, "Error creating project"

def update_project(user_id: int, project_id: str, updates: Dict[str, Any]) -> bool:
    """Update a project in the database for a specific user."""
    try:
        # Add updated_at timestamp
        updates["updated_at"] = datetime.utcnow()

        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$set": updates}
        )

        if result.matched_count > 0:
            logger.info(f"Updated project {project_id} for user {user_id}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating project {project_id} for user {user_id}: {e}")
        return False

def remove_project(user_id: int, project_id: str) -> bool:
    """Remove a project from the database for a specific user."""
    try:
        result = db_manager.db.projects.delete_one({
            "user_id": user_id,
            "project_id": project_id
        })

        if result.deleted_count > 0:
            logger.info(f"Removed project {project_id} for user {user_id}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error removing project {project_id} for user {user_id}: {e}")
        return False

def get_project(user_id: int, project_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific project for a user."""
    try:
        project_doc = db_manager.db.projects.find_one({
            "user_id": user_id,
            "project_id": project_id
        })

        if not project_doc:
            return None

        # Convert MongoDB document to expected format
        project_data = {
            "user_id": project_doc["user_id"],
            "name": project_doc["name"],
            "channels": project_doc["channels"],
            "created_at": project_doc["created_at"].isoformat() if isinstance(project_doc["created_at"], datetime) else project_doc["created_at"],
            "active": project_doc.get("active", True),
            "content_settings": project_doc.get("content_settings", {}),
            "image_settings": project_doc.get("image_settings", {"mode": "default", "custom_image_path": None}),
            "button_settings": project_doc.get("button_settings", {"enabled": False, "buttons": []}),
            "custom_prompt": project_doc.get("custom_prompt")
        }

        return project_data

    except Exception as e:
        logger.error(f"Error getting project {project_id} for user {user_id}: {e}")
        return None

def get_active_projects(user_id: int = None) -> Dict[str, Dict[str, Any]]:
    """Get active projects from the database for a specific user."""
    try:
        # If no user_id provided, return empty dict (for security)
        if user_id is None:
            return {}

        # Query active projects for this user
        projects_cursor = db_manager.db.projects.find({"user_id": user_id, "active": True})

        # Convert to the expected format (project_id as key)
        projects = {}
        for project_doc in projects_cursor:
            project_id = project_doc["project_id"]
            # Convert MongoDB document to expected format
            project_data = {
                "user_id": project_doc["user_id"],
                "name": project_doc["name"],
                "channels": project_doc["channels"],
                "created_at": project_doc["created_at"].isoformat() if isinstance(project_doc["created_at"], datetime) else project_doc["created_at"],
                "active": project_doc.get("active", True),
                "content_settings": project_doc.get("content_settings", {}),
                "image_settings": project_doc.get("image_settings", {"mode": "default", "custom_image_path": None}),
                "button_settings": project_doc.get("button_settings", {"enabled": False, "buttons": []}),
                "custom_prompt": project_doc.get("custom_prompt")
            }
            projects[project_id] = project_data

        return projects
    except Exception as e:
        logger.error(f"Error getting active projects for user {user_id}: {e}")
        return {}

def get_all_projects_for_posting() -> Dict[str, Dict[str, Any]]:
    """Get all projects from the database for posting operations (across all users)."""
    try:
        # Query all projects (for posting system that needs access to all projects)
        projects_cursor = db_manager.db.projects.find({})

        # Convert to the expected format (project_id as key)
        projects = {}
        for project_doc in projects_cursor:
            project_id = project_doc["project_id"]
            # Convert MongoDB document to expected format
            project_data = {
                "user_id": project_doc["user_id"],
                "name": project_doc["name"],
                "channels": project_doc["channels"],
                "created_at": project_doc["created_at"].isoformat() if isinstance(project_doc["created_at"], datetime) else project_doc["created_at"],
                "active": project_doc.get("active", True),
                "content_settings": project_doc.get("content_settings", {}),
                "image_settings": project_doc.get("image_settings", {"mode": "default", "custom_image_path": None}),
                "button_settings": project_doc.get("button_settings", {"enabled": False, "buttons": []}),
                "custom_prompt": project_doc.get("custom_prompt")
            }
            projects[project_id] = project_data

        return projects
    except Exception as e:
        logger.error(f"Error getting all projects for posting: {e}")
        return {}

# ============================================================================
# CONTENT TYPE FUNCTIONS
# ============================================================================

def get_content_types() -> Dict[str, Dict[str, Any]]:
    """Get all available content types."""
    try:
        content_types_cursor = db_manager.db.content_types.find({})

        # Convert to the expected format (type_id as key)
        content_types = {}
        for content_type_doc in content_types_cursor:
            type_id = content_type_doc["type_id"]
            # Convert MongoDB document to expected format
            content_type_data = {
                "name": content_type_doc["name"],
                "description": content_type_doc["description"],
                "parameters": content_type_doc["parameters"]
            }
            content_types[type_id] = content_type_data

        return content_types
    except Exception as e:
        logger.error(f"Error getting content types: {e}")
        return {}

def update_content_type_parameters(content_type: str, parameters: Dict[str, Any]) -> bool:
    """Update parameters for a content type."""
    try:
        # Add updated_at timestamp
        update_data = {
            "parameters": parameters,
            "updated_at": datetime.utcnow()
        }

        result = db_manager.db.content_types.update_one(
            {"type_id": content_type},
            {"$set": update_data}
        )

        if result.matched_count > 0:
            logger.info(f"Updated content type {content_type} parameters")
            return True
        else:
            logger.warning(f"Content type {content_type} not found")
            return False

    except Exception as e:
        logger.error(f"Error updating content type {content_type}: {e}")
        return False

def get_content_type_parameters(content_type: str) -> Dict[str, Any]:
    """Get parameters for a specific content type."""
    try:
        content_type_doc = db_manager.db.content_types.find_one({"type_id": content_type})

        if content_type_doc:
            return content_type_doc.get("parameters", {})
        else:
            logger.warning(f"Content type {content_type} not found")
            return {}

    except Exception as e:
        logger.error(f"Error getting content type {content_type} parameters: {e}")
        return {}

# ============================================================================
# POST HISTORY FUNCTIONS
# ============================================================================

def get_post_history(user_id: int, project_id: str = None, limit: int = 50) -> List[Dict[str, Any]]:
    """Get post history for a user, optionally filtered by project."""
    try:
        # Build query
        query = {"user_id": user_id}
        if project_id:
            query["project_id"] = project_id

        # Get posts sorted by timestamp (newest first)
        posts_cursor = db_manager.db.post_history.find(query).sort("timestamp", -1).limit(limit)

        # Convert to list format
        posts = []
        for post_doc in posts_cursor:
            post_data = {
                "project_id": post_doc["project_id"],
                "channel_id": post_doc["channel_id"],
                "content_type": post_doc["content_type"],
                "content": post_doc["content"],
                "timestamp": post_doc["timestamp"].isoformat() if isinstance(post_doc["timestamp"], datetime) else post_doc["timestamp"]
            }
            posts.append(post_data)

        return posts
    except Exception as e:
        logger.error(f"Error getting post history for user {user_id}: {e}")
        return []

def add_post_to_history(user_id: int, project_id: str, channel_id: str, content_type: str, content: str) -> bool:
    """Add a post to the history."""
    try:
        # Create post document
        post_doc = {
            "project_id": project_id,
            "user_id": user_id,
            "channel_id": channel_id,
            "content_type": content_type,
            "content": content,
            "timestamp": datetime.utcnow(),
            "created_at": datetime.utcnow()
        }

        # Insert the post
        db_manager.db.post_history.insert_one(post_doc)

        # Clean up old posts (keep only last 100 per project)
        posts_cursor = db_manager.db.post_history.find({
            "user_id": user_id,
            "project_id": project_id
        }).sort("timestamp", -1).skip(100)

        # Delete old posts
        old_post_ids = [post["_id"] for post in posts_cursor]
        if old_post_ids:
            db_manager.db.post_history.delete_many({"_id": {"$in": old_post_ids}})

        logger.info(f"Added post to history for project {project_id}")
        return True

    except Exception as e:
        logger.error(f"Error adding post to history: {e}")
        return False

def clear_post_history(user_id: int, project_id: str) -> bool:
    """Clear post history for a specific project."""
    try:
        result = db_manager.db.post_history.delete_many({
            "user_id": user_id,
            "project_id": project_id
        })

        logger.info(f"Cleared {result.deleted_count} posts from history for project {project_id}")
        return True

    except Exception as e:
        logger.error(f"Error clearing post history for project {project_id}: {e}")
        return False

def reset_last_posted_time(user_id: int, project_id: str, content_type: str) -> bool:
    """Reset the last posted time for a specific project and content type."""
    try:
        # Update the last_posted field in the project's content settings
        update_field = f"content_settings.{content_type}.last_posted"
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$unset": {update_field: ""},
             "$set": {"updated_at": datetime.utcnow()}}
        )

        if result.matched_count > 0:
            logger.info(f"Reset last posted time for project {project_id}, content type {content_type}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error resetting last posted time for project {project_id}: {e}")
        return False

# ============================================================================
# PROJECT SETTINGS FUNCTIONS
# ============================================================================

def update_project_content_settings(user_id: int, project_id: str, content_type: str, settings: Dict[str, Any]) -> bool:
    """Update content settings for a specific content type in a project."""
    try:
        # Use dot notation to update the specific content type settings
        update_field = f"content_settings.{content_type}"
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$set": {
                update_field: settings,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Updated content settings for project {project_id}, content type {content_type}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating content settings for project {project_id}: {e}")
        return False

def update_project_image_settings(user_id: int, project_id: str, settings: Dict[str, Any]) -> bool:
    """Update image settings for a project."""
    try:
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$set": {
                "image_settings": settings,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Updated image settings for project {project_id}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating image settings for project {project_id}: {e}")
        return False

def update_project_button_settings(user_id: int, project_id: str, settings: Dict[str, Any]) -> bool:
    """Update button settings for a project."""
    try:
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$set": {
                "button_settings": settings,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Updated button settings for project {project_id}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating button settings for project {project_id}: {e}")
        return False

def save_custom_image(user_id: int, project_id: str, image_path: str) -> str:
    """Save custom image path for a project."""
    # This function maintains the same interface but the path is still stored in filesystem
    # Only the reference is stored in MongoDB
    try:
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$set": {
                "image_settings.custom_image_path": image_path,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Updated custom image path for project {project_id}")
            return image_path
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return ""

    except Exception as e:
        logger.error(f"Error saving custom image for project {project_id}: {e}")
        return ""

def get_image_path() -> str:
    """Get the image directory path."""
    return IMAGE_DIR

def get_project_custom_prompt(user_id: int, project_id: str) -> str:
    """Get custom prompt for a project for a specific user."""
    try:
        project_doc = db_manager.db.projects.find_one({
            "user_id": user_id,
            "project_id": project_id
        })

        if project_doc:
            return project_doc.get("custom_prompt", "")
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return ""

    except Exception as e:
        logger.error(f"Error getting custom prompt for project {project_id}: {e}")
        return ""

def set_project_custom_prompt(user_id: int, project_id: str, prompt: str) -> bool:
    """Set custom prompt for a project for a specific user."""
    try:
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$set": {
                "custom_prompt": prompt,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Updated custom prompt for project {project_id}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error setting custom prompt for project {project_id}: {e}")
        return False

def remove_project_custom_prompt(user_id: int, project_id: str) -> bool:
    """Remove custom prompt for a project for a specific user."""
    try:
        result = db_manager.db.projects.update_one(
            {"user_id": user_id, "project_id": project_id},
            {"$unset": {"custom_prompt": ""},
             "$set": {"updated_at": datetime.utcnow()}}
        )

        if result.matched_count > 0:
            logger.info(f"Removed custom prompt for project {project_id}")
            return True
        else:
            logger.warning(f"Project {project_id} not found for user {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error removing custom prompt for project {project_id}: {e}")
        return False

# ============================================================================
# USER ACCESS CONTROL FUNCTIONS
# ============================================================================

def get_pro_users() -> List[int]:
    """Get list of pro user IDs."""
    try:
        users_cursor = db_manager.db.users.find({"is_pro": True})
        pro_users = [user_doc["user_id"] for user_doc in users_cursor]
        return pro_users
    except Exception as e:
        logger.error(f"Error getting pro users: {e}")
        return []

def get_all_users() -> List[Dict[str, Any]]:
    """Get list of all users with their details."""
    try:
        users_cursor = db_manager.db.users.find({})
        users = []
        for user in users_cursor:
            user_data = {
                "user_id": user["user_id"],
                "is_admin": user.get("is_admin", False),
                "is_pro": user.get("is_pro", False),
                "project_limit": user.get("project_limit", 0),
                "project_count": get_user_project_count(user["user_id"]),
                "created_at": user.get("created_at"),
                "updated_at": user.get("updated_at")
            }
            users.append(user_data)
        return users
    except Exception as e:
        logger.error(f"Error getting all users: {e}")
        return []

def get_admin_user_id() -> int:
    """Get the admin user ID."""
    try:
        admin_user = db_manager.db.users.find_one({"is_admin": True})
        if admin_user:
            return admin_user["user_id"]
        else:
            # Fallback to default admin ID
            return 1049516929
    except Exception as e:
        logger.error(f"Error getting admin user ID: {e}")
        return 1049516929

def is_user_pro(user_id: int) -> bool:
    """Check if a user has pro status (replaces is_user_approved)."""
    try:
        user = db_manager.db.users.find_one({"user_id": user_id})
        if user:
            return user.get("is_pro", False)
        return False
    except Exception as e:
        logger.error(f"Error checking pro status for {user_id}: {e}")
        return False

def create_user_if_not_exists(user_id: int) -> bool:
    """Create a new user with default settings if they don't exist."""
    try:
        existing_user = db_manager.db.users.find_one({"user_id": user_id})

        if not existing_user:
            # Create new user with is_pro = False by default
            user_doc = {
                "user_id": user_id,
                "is_admin": False,
                "is_pro": False,
                "project_limit": 0,  # No projects for non-pro users
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }

            db_manager.db.users.insert_one(user_doc)
            logger.info(f"Created new user {user_id} with is_pro=False")
            return True

        return False  # User already exists

    except Exception as e:
        logger.error(f"Error creating user {user_id}: {e}")
        return False

def is_user_admin(user_id: int) -> bool:
    """Check if a user is an admin."""
    try:
        user = db_manager.db.users.find_one({"user_id": user_id, "is_admin": True})
        return user is not None
    except Exception as e:
        logger.error(f"Error checking admin status for {user_id}: {e}")
        return False

# Removed duplicate is_user_pro function - using the one above

def get_user_project_limit(user_id: int) -> int:
    """Get the project limit for a user."""
    try:
        user = db_manager.db.users.find_one({"user_id": user_id})
        if user:
            return user.get("project_limit", 0)
        return 0
    except Exception as e:
        logger.error(f"Error getting project limit for {user_id}: {e}")
        return 0

def get_user_project_count(user_id: int) -> int:
    """Get the current number of projects for a user."""
    try:
        count = db_manager.db.projects.count_documents({"user_id": user_id})
        return count
    except Exception as e:
        logger.error(f"Error getting project count for {user_id}: {e}")
        return 0

def can_user_create_project(user_id: int) -> tuple[bool, str]:
    """Check if a user can create a new project. Returns (can_create, reason)."""
    try:
        # Check if user has pro status
        if not is_user_pro(user_id):
            return False, "User does not have pro status to use the bot"

        # Get user's current project count and limit
        current_count = get_user_project_count(user_id)
        project_limit = get_user_project_limit(user_id)

        # Check if user has reached their limit
        if current_count >= project_limit:
            return False, f"Project limit reached ({current_count}/{project_limit}). Contact admin for pro upgrade."

        return True, f"Can create project ({current_count + 1}/{project_limit})"

    except Exception as e:
        logger.error(f"Error checking project creation permission for {user_id}: {e}")
        return False, "Error checking permissions"

def add_pro_user(user_id: int, project_limit: int = 5) -> bool:
    """Add or update a user to pro status."""
    try:
        # Check if user already exists
        existing_user = db_manager.db.users.find_one({"user_id": user_id})

        if existing_user:
            # Update existing user to pro
            result = db_manager.db.users.update_one(
                {"user_id": user_id},
                {"$set": {
                    "is_pro": True,
                    "project_limit": project_limit,
                    "updated_at": datetime.utcnow()
                }}
            )
            logger.info(f"Updated user {user_id} to pro status (limit: {project_limit})")
        else:
            # Create new pro user
            user_doc = {
                "user_id": user_id,
                "is_admin": False,
                "is_pro": True,
                "project_limit": project_limit,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }

            db_manager.db.users.insert_one(user_doc)
            logger.info(f"Created new pro user {user_id} (limit: {project_limit})")

        return True

    except Exception as e:
        logger.error(f"Error adding pro user {user_id}: {e}")
        return False

def promote_user_to_pro(user_id: int, project_limit: int = 5) -> bool:
    """Promote a user to pro status with specified project limit."""
    try:
        result = db_manager.db.users.update_one(
            {"user_id": user_id},
            {"$set": {
                "is_pro": True,
                "project_limit": project_limit,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Promoted user {user_id} to pro status with limit {project_limit}")
            return True
        else:
            logger.warning(f"User {user_id} not found for pro promotion")
            return False

    except Exception as e:
        logger.error(f"Error promoting user {user_id} to pro: {e}")
        return False

def remove_pro_status(user_id: int) -> bool:
    """Remove pro status from a user (set is_pro to False)."""
    try:
        # Don't allow removing admin user's pro status
        admin_id = get_admin_user_id()
        if user_id == admin_id:
            logger.warning(f"Cannot remove pro status from admin user {user_id}")
            return False

        result = db_manager.db.users.update_one(
            {"user_id": user_id},
            {"$set": {
                "is_pro": False,
                "project_limit": 0,
                "updated_at": datetime.utcnow()
            }}
        )

        if result.matched_count > 0:
            logger.info(f"Removed pro status from user {user_id}")
            return True
        else:
            logger.warning(f"User {user_id} not found")
            return False

    except Exception as e:
        logger.error(f"Error removing pro status from user {user_id}: {e}")
        return False

def get_user_info(user_id: int) -> Optional[Dict[str, Any]]:
    """Get complete user information."""
    try:
        user = db_manager.db.users.find_one({"user_id": user_id})
        if user:
            return {
                "user_id": user["user_id"],
                "is_admin": user.get("is_admin", False),
                "is_pro": user.get("is_pro", False),
                "project_limit": user.get("project_limit", 0),
                "project_count": get_user_project_count(user_id),
                "created_at": user.get("created_at"),
                "updated_at": user.get("updated_at")
            }
        return None
    except Exception as e:
        logger.error(f"Error getting user info for {user_id}: {e}")
        return None

def migrate_to_pro_only_system():
    """
    Migrate from is_approved + is_pro system to is_pro-only system.
    This function removes the is_approved field and uses only is_pro.
    """
    try:
        logger.info("Starting migration to pro-only system...")

        # Get all users
        all_users = list(db_manager.db.users.find({}))
        logger.info(f"Found {len(all_users)} users to migrate")

        migration_stats = {
            "total_users": len(all_users),
            "admin_users": 0,
            "pro_users": 0,
            "regular_users": 0,
            "updated_users": 0,
            "errors": 0
        }

        for user in all_users:
            user_id = user.get("user_id")
            is_admin = user.get("is_admin", False)
            is_approved = user.get("is_approved", False)
            current_is_pro = user.get("is_pro", False)

            try:
                # Determine new is_pro status
                if is_admin:
                    # Admin users always remain pro
                    new_is_pro = True
                    migration_stats["admin_users"] += 1
                elif is_approved and current_is_pro:
                    # Approved pro users remain pro
                    new_is_pro = True
                    migration_stats["pro_users"] += 1
                else:
                    # All others become non-pro
                    new_is_pro = False
                    migration_stats["regular_users"] += 1

                # Update user document
                update_doc = {
                    "$set": {
                        "is_pro": new_is_pro,
                        "updated_at": datetime.utcnow()
                    },
                    "$unset": {
                        "is_approved": ""  # Remove is_approved field
                    }
                }

                result = db_manager.db.users.update_one(
                    {"user_id": user_id},
                    update_doc
                )

                if result.modified_count > 0:
                    migration_stats["updated_users"] += 1
                    logger.info(f"Migrated user {user_id}: is_pro={new_is_pro}")

            except Exception as e:
                logger.error(f"Error migrating user {user_id}: {e}")
                migration_stats["errors"] += 1

        logger.info("Migration completed!")
        logger.info(f"Migration Statistics:")
        logger.info(f"  Total users processed: {migration_stats['total_users']}")
        logger.info(f"  Admin users: {migration_stats['admin_users']}")
        logger.info(f"  Pro users: {migration_stats['pro_users']}")
        logger.info(f"  Regular users: {migration_stats['regular_users']}")
        logger.info(f"  Users updated: {migration_stats['updated_users']}")
        logger.info(f"  Errors: {migration_stats['errors']}")

        return migration_stats

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

# Initialize the database when the module is imported
initialize_database()
