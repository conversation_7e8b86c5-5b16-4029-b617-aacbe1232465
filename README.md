# 🤖 Telegram Auto-Posting Bot

A powerful, multi-user Telegram bot for automated content posting across multiple channels with advanced scheduling, content generation, and customization features.

## ✨ Features

### 🚀 Core Functionality
- **Multi-User Support**: Each user manages their own channels and projects independently
- **Multi-Channel Posting**: Post to multiple Telegram channels simultaneously
- **Automated Scheduling**: Set custom posting times with timezone support
- **Manual Override**: Instant manual posting that bypasses timing restrictions

### 📝 Content Types
- **Daily News Summary**: AI-generated news summaries using Gemini API
- **Crypto Prices**: Real-time cryptocurrency price updates
- **Health & Fitness**: AI-generated health and fitness content
- **Cricket News**: Latest cricket news and updates

### 🎨 Customization
- **Custom Images**: Upload custom images or use internet image search
- **Custom Buttons**: Add interactive buttons below posts
- **Flexible Scheduling**: Set posting frequency and specific times
- **Timezone Support**: India, USA, Russia timezone options

### 🛠️ Management
- **Project Settings**: Configure content types, images, buttons, and timing
- **Post History**: View posting history and statistics
- **Reset Functions**: Reset last posted times and clear history
- **Project Activation**: Enable/disable projects as needed

## 🔧 Installation

### Prerequisites
- Python 3.8 or higher
- Telegram Bot Token (from @BotFather)
- Google Gemini API Key
- NewsAPI Key (optional)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Auto-posting-bot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   Create a `.env` file in the root directory:
   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   GEMINI_API_KEY=your_gemini_api_key_here
   NEWS_API_KEY=your_news_api_key_here
   GNEWS_API_KEY=your_gnews_api_key_here
   
   # Gemini Prompts (optional - defaults provided)
   GEMINI_PROMPT_DAILY_NEWS=Your custom daily news prompt
   GEMINI_PROMPT_CRICKET_NEWS=Your custom cricket news prompt
   GEMINI_PROMPT_HEALTH_FITNESS=Your custom health fitness prompt
   ```

4. **Run the bot**
   ```bash
   python bot.py
   ```

## 📱 Usage

### Getting Started
1. Start a conversation with your bot on Telegram
2. Send `/start` to begin
3. Use `/addchannel` to add your first channel
4. Use `/addprojects` to create your first project
5. Configure your project settings and start posting!

### Commands
- `/start` - Initialize the bot and show main menu
- `/addchannel` - Add a new Telegram channel
- `/channels` - View and manage your channels
- `/addprojects` - Create a new posting project
- `/projects` - View and manage your projects
- `/help` - Show help information
- `/cancel` - Cancel current operation

### Project Configuration
Each project can be configured with:
- **Content Type**: Choose from available content generators
- **Posting Schedule**: Set frequency and specific times
- **Image Settings**: Default, custom upload, or internet search
- **Custom Buttons**: Add interactive buttons to posts
- **Timezone**: Select your preferred timezone

## 🏗️ Architecture

### Core Components
- `bot.py` - Main bot application and command handlers
- `content_poster.py` - Content posting engine and scheduling
- `database.py` - Data persistence and user isolation
- `handlers/` - Telegram update handlers organized by functionality
- `utils/` - Utility functions for keyboards and sessions

### Content Generators
- `gemini_api.py` - AI content generation using Google Gemini
- `news_api.py` - News content fetching and processing
- `cricket_api.py` - Cricket news generation
- `crypto_api.py` - Cryptocurrency price updates

### Supporting Modules
- `image_search.py` - Internet image search and management
- `timezone_utils.py` - Timezone handling and conversions
- `prompt_config.py` - Configurable AI prompts

## 🔒 Security Features

- **User Data Isolation**: Each user can only access their own data
- **Access Control**: Whitelist-based user authentication
- **Input Validation**: Comprehensive input sanitization
- **Error Handling**: Graceful error recovery and logging

## 🚀 Production Deployment

### Environment Setup
1. Ensure all environment variables are properly configured
2. Set up proper logging and monitoring
3. Configure automatic restarts (systemd, PM2, etc.)
4. Set up backup procedures for the `data/` directory

### Monitoring
- Monitor bot logs for errors and performance
- Track API usage and rate limits
- Monitor disk space for image storage
- Set up alerts for bot downtime

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the logs for error messages
- Ensure all API keys are valid and have sufficient quota
- Verify bot permissions in target channels
- Check network connectivity and firewall settings

## 🔄 Updates

The bot supports hot-reloading of configuration changes. Restart the bot to apply:
- New environment variables
- Updated prompts in environment variables
- Code changes

---

**Made with ❤️ for automated content management**
