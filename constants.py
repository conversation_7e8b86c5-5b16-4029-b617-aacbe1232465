"""
Configuration constants for the Telegram Auto-Posting Bot.
Centralizes all configurable values for better maintainability.
"""

# =============================================================================
# USER MANAGEMENT CONSTANTS
# =============================================================================

# Admin user ID (should match the one in access_control.py)
ADMIN_USER_ID = 1049516929

# Project limits
DEFAULT_PROJECT_LIMIT = 0  # For regular users
PRO_PROJECT_LIMIT = 5      # For pro users

# =============================================================================
# DATABASE CONSTANTS
# =============================================================================

# Post history management
MAX_POSTS_PER_PROJECT = 100  # Maximum posts to keep in history per project

# Connection settings
DB_CONNECTION_TIMEOUT = 30  # seconds
DB_OPERATION_TIMEOUT = 10   # seconds

# =============================================================================
# CONTENT POSTING CONSTANTS
# =============================================================================

# Posting intervals (in hours)
DEFAULT_POSTING_INTERVAL = 24
MIN_POSTING_INTERVAL = 1
MAX_POSTING_INTERVAL = 168  # 1 week

# Content generation limits
MAX_CONTENT_LENGTH = 1024   # Maximum caption length for Telegram
MAX_CUSTOM_PROMPT_LENGTH = 2000  # Maximum custom prompt length

# Retry settings
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 5
EXPONENTIAL_BACKOFF_MULTIPLIER = 2

# =============================================================================
# API RATE LIMITING
# =============================================================================

# Telegram API limits
TELEGRAM_API_RATE_LIMIT = 30  # requests per second
TELEGRAM_BULK_RATE_LIMIT = 20  # bulk operations per minute

# External API limits
GEMINI_API_RATE_LIMIT = 60    # requests per minute
NEWS_API_RATE_LIMIT = 1000    # requests per day
GNEWS_API_RATE_LIMIT = 100    # requests per day

# =============================================================================
# FILE MANAGEMENT CONSTANTS
# =============================================================================

# Image settings
MAX_IMAGE_SIZE_MB = 10
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
IMAGE_COMPRESSION_QUALITY = 85

# Directory paths
DATA_DIR = "data"
IMAGE_DIR = "data/images"
INTERNET_IMAGES_DIR = "data/internet_images"
LOGS_DIR = "logs"

# =============================================================================
# SECURITY CONSTANTS
# =============================================================================

# Input validation
MAX_CHANNEL_NAME_LENGTH = 100
MAX_PROJECT_NAME_LENGTH = 50
MAX_BUTTON_TEXT_LENGTH = 30
MAX_BUTTON_URL_LENGTH = 500

# Session timeouts
CONVERSATION_TIMEOUT = 300  # 5 minutes
SESSION_CLEANUP_INTERVAL = 3600  # 1 hour

# =============================================================================
# LOGGING CONSTANTS
# =============================================================================

# Log levels
DEFAULT_LOG_LEVEL = "INFO"
DEBUG_LOG_LEVEL = "DEBUG"

# Log file settings
MAX_LOG_FILE_SIZE_MB = 50
LOG_BACKUP_COUNT = 5

# =============================================================================
# CONTENT TYPE CONSTANTS
# =============================================================================

# Available content types
CONTENT_TYPES = {
    "daily_news": {
        "name": "Daily News Summary",
        "default_interval": 24,
        "default_time": {"hour": 8, "minute": 0}
    },
    "crypto_prices": {
        "name": "Crypto Prices",
        "default_interval": 24,
        "default_time": {"hour": 9, "minute": 0}
    },
    "cricket_news": {
        "name": "Cricket News",
        "default_interval": 24,
        "default_time": {"hour": 10, "minute": 0}
    },
    "health_fitness": {
        "name": "Health & Fitness",
        "default_interval": 24,
        "default_time": {"hour": 7, "minute": 0}
    },
    "custom_content": {
        "name": "Custom Content",
        "default_interval": 24,
        "default_time": {"hour": 12, "minute": 0}
    }
}

# =============================================================================
# TIMEZONE CONSTANTS
# =============================================================================

# Supported timezones
SUPPORTED_TIMEZONES = {
    "India": "Asia/Kolkata",
    "USA": "America/New_York",
    "Russia": "Europe/Moscow"
}

# Default timezone
DEFAULT_TIMEZONE = "India"

# =============================================================================
# ERROR HANDLING CONSTANTS
# =============================================================================

# Error message templates
ERROR_MESSAGES = {
    "generic": "⚠️ An error occurred. The issue has been reported to the administrator.",
    "unauthorized": "🤖 Hello! I am an auto-posting bot with lots of advanced features for managing content across multiple Telegram channels. This is a premium paid service. Please contact @the_titanium_admin to purchase access.",
    "project_limit": "Project limit reached ({current}/{limit}). Contact admin for pro upgrade.",
    "channel_exists": "⚠️ Channel '{channel_name}' is already in your list.",
    "channel_owned": "⚠️ This channel is already being used by another user. Each channel can only be managed by one user at a time.",
    "invalid_input": "⚠️ Invalid input. Please try again or use /cancel to abort.",
    "operation_cancelled": "❌ Operation cancelled.",
    "permission_denied": "⚠️ You don't have permission to perform this action."
}

# =============================================================================
# SUCCESS MESSAGE CONSTANTS
# =============================================================================

SUCCESS_MESSAGES = {
    "channel_added": "✅ Channel '{channel_name}' added successfully!\n\nYou can now create projects for this channel using /addprojects.",
    "project_created": "✅ Project created successfully ({current}/{limit})",
    "settings_updated": "✅ Settings updated successfully!",
    "content_posted": "✅ Content posted successfully to {channel_count} channel(s)!",
    "user_approved": "✅ User {user_id} has been approved and added to the system.",
    "user_promoted": "✅ User {user_id} has been promoted to pro status."
}

# =============================================================================
# VALIDATION PATTERNS
# =============================================================================

# Regular expressions for input validation
VALIDATION_PATTERNS = {
    "time_format": r"^([01]?[0-9]|2[0-3]):([0-5][0-9])$",  # HH:MM format
    "url_format": r"^https?://[^\s/$.?#].[^\s]*$",          # Basic URL validation
    "channel_id": r"^-100\d{10,}$",                         # Telegram channel ID format
}

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
FEATURES = {
    "internet_image_search": True,
    "content_caching": False,      # Future feature
    "advanced_analytics": False,   # Future feature
    "webhook_support": False,      # Future feature
    "multi_language": False,       # Future feature
}
