"""
Access Control Module for Telegram Auto-Posting Bot
Handles user authentication, whitelist management, and admin functions.
Now uses MongoDB for user management.
"""
import logging
from functools import wraps
from typing import List, Dict, Any
from telegram import Update
from telegram.ext import CallbackContext

# Import database functions
import database as db

# Configure logging
logger = logging.getLogger(__name__)

# Admin user ID
ADMIN_USER_ID = 1049516929

class AccessControl:
    """Manages user access control and whitelist functionality using MongoDB."""

    def __init__(self):
        # Initialize database to ensure admin user exists
        db.initialize_database()
        logger.info("Access control initialized with MongoDB backend")

    def is_user_pro(self, user_id: int) -> bool:
        """Check if a user has pro status to use the bot."""
        return db.is_user_pro(user_id)

    def add_user(self, user_id: int, project_limit: int = 5) -> bool:
        """Add a user with pro status."""
        try:
            success = db.add_pro_user(user_id, project_limit)
            if success:
                logger.info(f"Added pro user {user_id} (limit: {project_limit})")
            return success
        except Exception as e:
            logger.error(f"Error adding user {user_id}: {e}")
            return False

    def remove_user(self, user_id: int) -> bool:
        """Remove pro status from a user (except admin)."""
        try:
            if user_id == ADMIN_USER_ID:
                return False  # Cannot remove admin

            success = db.remove_pro_status(user_id)
            if success:
                logger.info(f"Removed pro status from user {user_id}")
            return success
        except Exception as e:
            logger.error(f"Error removing user {user_id}: {e}")
            return False

    def promote_to_pro(self, user_id: int, project_limit: int = 5) -> bool:
        """Promote a user to pro status."""
        try:
            success = db.promote_user_to_pro(user_id, project_limit)
            if success:
                logger.info(f"Promoted user {user_id} to pro status with limit {project_limit}")
            return success
        except Exception as e:
            logger.error(f"Error promoting user {user_id} to pro: {e}")
            return False

    def get_pro_users(self) -> List[int]:
        """Get list of all pro users."""
        return db.get_pro_users()

    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get list of all users with their details."""
        return db.get_all_users()

    def is_admin(self, user_id: int) -> bool:
        """Check if a user is the admin."""
        return db.is_user_admin(user_id)

    def is_pro(self, user_id: int) -> bool:
        """Check if a user has pro status."""
        return db.is_user_pro(user_id)

    def get_user_info(self, user_id: int) -> dict:
        """Get complete user information."""
        user_info = db.get_user_info(user_id)
        return user_info if user_info else {}

    def get_user_project_stats(self, user_id: int) -> dict:
        """Get user's project statistics."""
        try:
            current_count = db.get_user_project_count(user_id)
            project_limit = db.get_user_project_limit(user_id)
            can_create, reason = db.can_user_create_project(user_id)

            return {
                "current_count": current_count,
                "project_limit": project_limit,
                "can_create": can_create,
                "reason": reason
            }
        except Exception as e:
            logger.error(f"Error getting project stats for user {user_id}: {e}")
            return {"current_count": 0, "project_limit": 0, "can_create": False, "reason": "Error"}

# Global access control instance
access_control = AccessControl()

def require_approval(func):
    """Decorator to require pro status before executing command."""
    @wraps(func)
    async def wrapper(update: Update, context: CallbackContext, *args, **kwargs):
        try:
            user_id = update.effective_user.id

            # Auto-create user if they don't exist
            db.create_user_if_not_exists(user_id)

            # Check if user has pro status
            if not access_control.is_user_pro(user_id):
                # Send rejection message to non-pro user
                await update.effective_message.reply_text(
                    "🤖 Hello! I am an auto-posting bot with lots of advanced features for "
                    "managing content across multiple Telegram channels. This is a premium "
                    "paid service. Please contact @the_titanium_admin to purchase access."
                )
                logger.warning(f"Non-pro user {user_id} attempted to use command: {func.__name__}")
                return

            # User has pro status, execute the original function
            return await func(update, context, *args, **kwargs)

        except Exception as e:
            logger.error(f"Error in access control wrapper for {func.__name__}: {e}")
            # Send error to admin instead of user
            await send_error_to_admin(context, f"Access control error in {func.__name__}: {e}")

            # Send generic message to user
            if update and update.effective_message:
                await update.effective_message.reply_text(
                    "⚠️ An error occurred. The issue has been reported to the administrator."
                )

    return wrapper

async def send_error_to_admin(context: CallbackContext, error_message: str) -> None:
    """Send error message to admin user."""
    try:
        await context.bot.send_message(
            chat_id=ADMIN_USER_ID,
            text=f"🚨 *Bot Error Report*\n\n"
                 f"```\n{error_message}\n```",
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Failed to send error to admin: {e}")

async def handle_admin_add_user(update: Update, context: CallbackContext) -> None:
    """Handle admin command to add new approved user."""
    try:
        user_id = update.effective_user.id

        # Check if user is admin
        if not access_control.is_admin(user_id):
            await update.message.reply_text("❌ You are not authorized to use this command.")
            return

        # Check if user_id argument is provided
        if not context.args or len(context.args) < 1:
            await update.message.reply_text(
                "❌ Usage: /a <user_id> [project_limit]\n"
                "Examples:\n"
                "• /a 123456789 - Add pro user (5 projects)\n"
                "• /a 123456789 10 - Add pro user (10 projects)"
            )
            return

        try:
            new_user_id = int(context.args[0])
        except ValueError:
            await update.message.reply_text("❌ Invalid user ID. Please provide a numeric user ID.")
            return

        # Get project limit (default 5)
        project_limit = 5
        if len(context.args) > 1:
            try:
                project_limit = int(context.args[1])
                if project_limit < 0:
                    project_limit = 5
            except ValueError:
                await update.message.reply_text("❌ Invalid project limit. Using default (5).")

        # Add user as pro
        if access_control.add_user(new_user_id, project_limit):
            await update.message.reply_text(
                f"✅ User {new_user_id} has been added as pro user ({project_limit} projects)."
            )
            logger.info(f"Admin {user_id} added user {new_user_id} as pro (limit: {project_limit})")
        else:
            await update.message.reply_text(
                f"ℹ️ User {new_user_id} is already a pro user."
            )

    except Exception as e:
        logger.error(f"Error in admin add user command: {e}")
        await send_error_to_admin(context, f"Error in admin add user command: {e}")
        await update.message.reply_text("⚠️ An error occurred while processing the command.")

async def handle_admin_promote_user(update: Update, context: CallbackContext) -> None:
    """Handle admin command to promote user to pro status."""
    try:
        user_id = update.effective_user.id

        # Check if user is admin
        if not access_control.is_admin(user_id):
            await update.message.reply_text("❌ You are not authorized to use this command.")
            return

        # Check if user_id argument is provided
        if not context.args or len(context.args) < 1:
            await update.message.reply_text(
                "❌ Usage: /promote <user_id> [project_limit]\n"
                "Examples:\n"
                "• /promote 123456789 - Promote to pro (5 projects)\n"
                "• /promote 123456789 10 - Promote to pro (10 projects)"
            )
            return

        try:
            target_user_id = int(context.args[0])
        except ValueError:
            await update.message.reply_text("❌ Invalid user ID. Please provide a numeric user ID.")
            return

        # Get project limit (default 5)
        project_limit = 5
        if len(context.args) > 1:
            try:
                project_limit = int(context.args[1])
                if project_limit < 1:
                    await update.message.reply_text("❌ Project limit must be at least 1.")
                    return
            except ValueError:
                await update.message.reply_text("❌ Invalid project limit. Please provide a number.")
                return

        # Check if user exists and has pro status
        if not access_control.is_user_pro(target_user_id):
            await update.message.reply_text(
                f"❌ User {target_user_id} does not have pro status. Add them first with /a command."
            )
            return

        # Promote user to pro
        if access_control.promote_to_pro(target_user_id, project_limit):
            await update.message.reply_text(
                f"✅ User {target_user_id} has been promoted to pro status with {project_limit} project limit."
            )
            logger.info(f"Admin {user_id} promoted user {target_user_id} to pro (limit: {project_limit})")
        else:
            await update.message.reply_text(
                f"⚠️ Failed to promote user {target_user_id}. Please try again."
            )

    except Exception as e:
        logger.error(f"Error in admin promote user command: {e}")
        await send_error_to_admin(context, f"Error in admin promote user command: {e}")
        await update.message.reply_text("⚠️ An error occurred while processing the command.")

async def handle_admin_list_users(update: Update, context: CallbackContext) -> None:
    """Handle admin command to list approved users with detailed information."""
    try:
        user_id = update.effective_user.id

        # Check if user is admin
        if not access_control.is_admin(user_id):
            await update.message.reply_text("❌ You are not authorized to use this command.")
            return

        all_users = access_control.get_all_users()

        if all_users:
            # Separate pro and non-pro users
            pro_users = [user for user in all_users if user.get("is_pro", False)]
            non_pro_users = [user for user in all_users if not user.get("is_pro", False)]

            user_details = []

            if pro_users:
                user_details.append("⭐ *Pro Users:*")
                for user_info in sorted(pro_users, key=lambda x: x['user_id']):
                    status_icons = []
                    if user_info.get("is_admin", False):
                        status_icons.append("👑")
                    status_icons.append("⭐")

                    status = " ".join(status_icons)
                    project_count = user_info.get("project_count", 0)
                    project_limit = user_info.get("project_limit", 0)
                    user_details.append(f"{status} {user_info['user_id']} ({project_count}/{project_limit})")
                user_details.append("")

            if non_pro_users:
                user_details.append("👤 *Non-Pro Users:*")
                for user_info in sorted(non_pro_users, key=lambda x: x['user_id']):
                    project_count = user_info.get("project_count", 0)
                    user_details.append(f"👤 {user_info['user_id']} ({project_count}/0)")

            user_list = "\n".join(user_details)
            await update.message.reply_text(
                f"👥 *All Users ({len(all_users)}):*\n\n{user_list}\n\n"
                f"*Legend:*\n👑 Admin | ⭐ Pro | 👤 Regular\n(projects/limit)",
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text("📝 No users found.")

    except Exception as e:
        logger.error(f"Error in admin list users command: {e}")
        await send_error_to_admin(context, f"Error in admin list users command: {e}")
        await update.message.reply_text("⚠️ An error occurred while processing the command.")

async def handle_admin_user_info(update: Update, context: CallbackContext) -> None:
    """Handle admin command to get detailed user information."""
    try:
        user_id = update.effective_user.id

        # Check if user is admin
        if not access_control.is_admin(user_id):
            await update.message.reply_text("❌ You are not authorized to use this command.")
            return

        # Check if user_id argument is provided
        if not context.args or len(context.args) != 1:
            await update.message.reply_text(
                "❌ Usage: /userinfo <user_id>\n"
                "Example: /userinfo 123456789"
            )
            return

        try:
            target_user_id = int(context.args[0])
        except ValueError:
            await update.message.reply_text("❌ Invalid user ID. Please provide a numeric user ID.")
            return

        user_info = access_control.get_user_info(target_user_id)

        if user_info:
            status_parts = []
            if user_info.get("is_admin", False):
                status_parts.append("👑 Admin")
            if user_info.get("is_pro", False):
                status_parts.append("⭐ Pro")
            else:
                status_parts.append("❌ Not Pro")

            status = " | ".join(status_parts)
            project_stats = access_control.get_user_project_stats(target_user_id)

            info_text = (
                f"👤 *User Information:*\n\n"
                f"**User ID:** {target_user_id}\n"
                f"**Status:** {status}\n"
                f"**Projects:** {project_stats['current_count']}/{project_stats['project_limit']}\n"
                f"**Can Create:** {'✅ Yes' if project_stats['can_create'] else '❌ No'}\n"
                f"**Reason:** {project_stats['reason']}\n"
            )

            if user_info.get("created_at"):
                info_text += f"**Created:** {user_info['created_at'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(user_info['created_at'], 'strftime') else user_info['created_at']}\n"

            await update.message.reply_text(info_text, parse_mode='Markdown')
        else:
            await update.message.reply_text(f"❌ User {target_user_id} not found in the system.")

    except Exception as e:
        logger.error(f"Error in admin user info command: {e}")
        await send_error_to_admin(context, f"Error in admin user info command: {e}")
        await update.message.reply_text("⚠️ An error occurred while processing the command.")
