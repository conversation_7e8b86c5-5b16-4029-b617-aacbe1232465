"""
Prompt Configuration Module for Telegram Auto-Posting Bot

This module handles loading and managing configurable prompts for different content types
from environment variables and JSON configuration files. It provides fallback prompts if
environment variables are missing.
"""

import os
import json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# Path to the JSON configuration file (optional - fallback only)
PROMPTS_JSON_FILE = "prompts.json"

# Default fallback prompts (matching current functionality)
DEFAULT_PROMPTS = {
    "daily_news": """Generate a daily news summary for {country} for TODAY, {current_date}.

IMPORTANT: You MUST include ONLY THE MOST RECENT NEWS from the past 24 hours. Do NOT include old news from previous days or weeks.
Use your knowledge cutoff to determine what is current and what is not. If you're unsure if something is current, do not include it.

Format the summary EXACTLY as follows:
- Start with "*DAILY NEWS SUMMARY*" (bold title)
- Each news item should be in italics using underscores: _News item text here._
- Add one blank line between each news item
- Include relevant emojis for each news item
- Focus on the most important and impactful news
- Include a mix of categories: politics, economy, technology, sports, entertainment, etc.

Example format:
*DAILY NEWS SUMMARY*

_Prime Minister announces new infrastructure development plan worth ₹2 lakh crore._ 🏗️💰

_Indian Space Research Organisation successfully launches communication satellite._ 🚀🛰️

_Stock markets reach new highs as IT sector shows strong growth._ 📈💼

IMPORTANT: The entire summary MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit.

CRITICAL: All news items MUST be from the LAST 24-48 HOURS ONLY. Do NOT include old news.""",

    "cricket_news": """Based on the following latest sports news from India, filter and select the 5 most relevant CRICKET-specific news items and convert them into catchy, engaging format:

{news_data}

INSTRUCTIONS:
1. Select ONLY cricket-related news from the above articles
2. If there are fewer than 5 cricket news items, create engaging cricket content based on current cricket context
3. Format the output EXACTLY as follows:

🏏 *CRICKET NEWS AND UPDATES*

_News 1 here._ 🏆

_News 2 here._ 🎯

_News 3 here._ 🔥

_News 4 here._ ⚡

_News 5 here._ 🌟

REQUIREMENTS:
- Keep each news item under 25 words to fit within 1000 characters total
- Each news item should be concise and engaging
- Use the exact emoji sequence (🏆, 🎯, 🔥, ⚡, 🌟)
- Make the content catchy and exciting for cricket fans
- Focus on the most recent and relevant cricket updates
- MUST include all 5 news items with their respective emojis""",

    "health_fitness": """Generate valuable health, nutrition, and fitness content for {current_date}.

IMPORTANT: Create a well-structured, informative post that provides genuinely useful health and fitness advice.
Focus on creating content that is readable, valuable, and presents new information each time.

Format requirements:
- Start with "💪 *HEALTH & FITNESS*" (bold title)
- Use clear sections with emojis
- Include practical tips that people can actually implement
- Make it engaging and motivational
- Vary the topics each time (nutrition, exercise, mental health, wellness tips, etc.)
- Use italics for emphasis: _important points_
- Include relevant emojis throughout

Example topics to rotate:
- Nutrition tips and healthy recipes
- Exercise routines and workout advice  
- Mental health and stress management
- Sleep hygiene and recovery
- Hydration and supplements
- Injury prevention
- Healthy lifestyle habits

IMPORTANT: The entire post MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit.

Focus on providing ACTIONABLE advice that adds real value to readers' health and fitness journey.""",

    "crypto_prices": """Generate a daily cryptocurrency price update for {current_date}.

IMPORTANT: You MUST provide the MOST CURRENT cryptocurrency information you have access to.
If you're unsure about current prices, focus on the trends and general market conditions instead.

Format the update EXACTLY as follows:
- Start with "💰 *CRYPTO MARKET UPDATE*" (bold title)
- Include major cryptocurrencies: Bitcoin (BTC), Ethereum (ETH), Solana (SOL), etc.
- Show prices in USD format: $XX,XXX.XX
- Include 24h percentage changes with + or - signs
- Add relevant emojis for each coin
- Include market sentiment and trends
- Add one trending/notable coin
- Include top gainer and top loser

Example format:
💰 *CRYPTO MARKET UPDATE*

🟠 _Bitcoin (BTC): $43,250.00 (+2.5%)_
🔷 _Ethereum (ETH): $2,580.00 (+1.8%)_  
🟣 _Solana (SOL): $98.50 (+4.2%)_

📈 _Top Gainer: AVAX (+12.3%)_
📉 _Top Loser: DOGE (-3.1%)_

_Market showing bullish momentum with increased institutional adoption._ 🚀

IMPORTANT: The entire update MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit.

CRITICAL: Focus on providing the MOST CURRENT information available to you.""",

    "custom_content": """Generate a mind-blowing fact transformed into a highly creative, visually stunning social media post. Each post must use a completely different and unique creative style, focusing on surprising and innovative combinations of:



Vastly diverse symbolic elements: Use any creative symbol, emoji, or character arrangement to form unique visual structures, dividers, or embellishments. This should not be limited to traditional borders but can be abstract, thematic, or narrative-driven.

Unconventional emoji patterns and sequences: Think beyond simple chains; create visual "paths," "bursts," or "clusters" with emojis.

Dynamic text formatting: Play with variable bolding, italics, spacing, capitalization (e.g., small caps, alternating caps), line breaks, and indentation to create visual interest and emphasize points.

Distinctive rhythm and flow: Vary sentence length and structure to create a unique reading pace and emotional resonance.

Engaging storytelling: Frame the fact with a compelling narrative hook, build-up, and impactful reveal.

Creative use of white space: Deliberately use blank lines and spaces to shape the visual appeal and focus attention.

Overall aesthetic surprise: The goal is for each post to look and feel entirely different from the last, like a distinct piece of visual art.

Surprise me each time with a fresh, unexpected aesthetic, thematic approach, and visual composition. Do not repeat specific border styles.

text word limit - 100

example -

╔═════ ೋღ 🍯 ღೋ ═════╗



     𝙃𝙊𝙉𝙀𝙔 𝙁𝘼𝘾𝙏 𝙏𝙃𝘼𝙏 𝘽𝙇𝙊𝙒𝙎 𝙈𝙄𝙉𝘿𝙎!



╚═════ ೋღ 🌟 ღೋ ═════╝

🕰️✨ Over 3,000 Years Old... and Still Delicious?!



That’s right — ancient Egyptian tombs were found with sealed jars of honey...

💀⏳ 𝙁𝙧𝙤𝙢 𝙖 𝙡𝙤𝙣𝙜-𝙜𝙤𝙣𝙚 𝙚𝙧𝙖…



But open the lid, and it's still golden, gooey, & good to eat! 🤯🍯

━━━ ❖ ━━━

example 2 -

Generate a mind-blowing fact transformed into a highly creative, visually stunning social media post. Each post must use a completely different and unique creative style, focusing on surprising and innovative combinations of:



Vastly diverse symbolic elements: Use any creative symbol, emoji, or character arrangement to form unique visual structures, dividers, or embellishments. This should not be limited to traditional borders but can be abstract, thematic, or narrative-driven.

Unconventional emoji patterns and sequences: Think beyond simple chains; create visual "paths," "bursts," or "clusters" with emojis.

Dynamic text formatting: Play with variable bolding, italics, spacing, capitalization (e.g., small caps, alternating caps), line breaks, and indentation to create visual interest and emphasize points.

Distinctive rhythm and flow: Vary sentence length and structure to create a unique reading pace and emotional resonance.

Engaging storytelling: Frame the fact with a compelling narrative hook, build-up, and impactful reveal.

Creative use of white space: Deliberately use blank lines and spaces to shape the visual appeal and focus attention.

Overall aesthetic surprise: The goal is for each post to look and feel entirely different from the last, like a distinct piece of visual art.

Surprise me each time with a fresh, unexpected aesthetic, thematic approach, and visual composition. Do not repeat specific border styles.

text word limit - 100

example -

╔═════ ೋღ 🍯 ღೋ ═════╗



     𝙃𝙊𝙉𝙀𝙔 𝙁𝘼𝘾𝙏 𝙏𝙃𝘼𝙏 𝘽𝙇𝙊𝙒𝙎 𝙈𝙄𝙉𝘿𝙎!



╚═════ ೋღ 🌟 ღೋ ═════╝

🕰️✨ Over 3,000 Years Old... and Still Delicious?!



That’s right — ancient Egyptian tombs were found with sealed jars of honey...

💀⏳ 𝙁𝙧𝙤𝙢 𝙖 𝙡𝙤𝙣𝙜-𝙜𝙤𝙣𝙚 𝙚𝙧𝙖…



But open the lid, and it's still golden, gooey, & good to eat! 🤯🍯

━━━ ❖ ━━━"""
}

def load_prompts_from_json() -> dict:
    """
    Load prompts from the JSON configuration file.

    Returns:
        dict: Dictionary of prompts loaded from JSON file, or empty dict if file not found
    """
    try:
        if os.path.exists(PROMPTS_JSON_FILE):
            with open(PROMPTS_JSON_FILE, 'r', encoding='utf-8') as f:
                prompts_data = json.load(f)
                # Extract just the prompt text from each content type
                prompts = {}
                for content_type, data in prompts_data.items():
                    if isinstance(data, dict) and 'prompt' in data:
                        prompts[content_type] = data['prompt']
                    else:
                        prompts[content_type] = str(data)
                logger.info(f"Successfully loaded {len(prompts)} prompts from {PROMPTS_JSON_FILE}")
                return prompts
        else:
            logger.warning(f"Prompts JSON file {PROMPTS_JSON_FILE} not found")
            return {}
    except Exception as e:
        logger.error(f"Error loading prompts from JSON file: {str(e)}")
        return {}

def get_prompt(content_type: str, custom_prompt: str = None, **kwargs) -> str:
    """
    Get the configured prompt for a specific content type.

    Priority order:
    1. Custom project prompt (if provided)
    2. Environment variables (if set and not empty)
    3. JSON configuration file (prompts.json)
    4. Default fallback prompts

    Args:
        content_type (str): The content type ('daily_news', 'cricket_news', 'health_fitness', 'crypto_prices')
        custom_prompt (str, optional): Custom prompt for this specific project
        **kwargs: Variables to format into the prompt (e.g., country, current_date)

    Returns:
        str: The formatted prompt ready to send to Gemini API
    """
    # Map content types to environment variable names
    env_var_map = {
        "daily_news": "GEMINI_PROMPT_DAILY_NEWS",
        "cricket_news": "GEMINI_PROMPT_CRICKET_NEWS",
        "health_fitness": "GEMINI_PROMPT_HEALTH_FITNESS",
        "crypto_prices": "GEMINI_PROMPT_CRYPTO_PRICES",
        "custom_content": "GEMINI_PROMPT_CUSTOM_CONTENT"
    }

    if content_type not in env_var_map:
        logger.error(f"Unknown content type: {content_type}")
        return f"Error: Unknown content type '{content_type}'"

    env_var_name = env_var_map[content_type]
    prompt = None
    source = "unknown"

    # 1. Try custom prompt first (highest priority)
    if custom_prompt and custom_prompt.strip():
        prompt = custom_prompt
        source = "custom project prompt"

    # 2. Try to get prompt from environment variable
    if not prompt:
        env_prompt = os.getenv(env_var_name)
        if env_prompt and env_prompt.strip():
            prompt = env_prompt
            source = "environment variable"

    # 3. If not found in env, try JSON configuration file
    if not prompt:
        json_prompts = load_prompts_from_json()
        if content_type in json_prompts:
            prompt = json_prompts[content_type]
            source = "JSON configuration"

    # 4. If still not found, use default fallback
    if not prompt:
        prompt = DEFAULT_PROMPTS.get(content_type, "")
        source = "default fallback"

        if not prompt:
            logger.error(f"No prompt available for content type: {content_type}")
            return f"Error: No prompt configured for content type '{content_type}'"

    # Format the prompt with provided variables
    try:
        formatted_prompt = prompt.format(**kwargs)
        logger.info(f"Successfully loaded and formatted prompt for {content_type} from {source}")
        return formatted_prompt
    except KeyError as e:
        logger.error(f"Missing variable {e} for prompt formatting in {content_type}")
        # Return unformatted prompt if formatting fails, but escape problematic characters
        # Replace curly braces that might cause Markdown parsing issues
        safe_prompt = prompt.replace('{', '\\{').replace('}', '\\}')
        return safe_prompt
    except Exception as e:
        logger.error(f"Error formatting prompt for {content_type}: {str(e)}")
        # Return unformatted prompt if formatting fails, but escape problematic characters
        safe_prompt = prompt.replace('{', '\\{').replace('}', '\\}')
        return safe_prompt

def get_daily_news_prompt(country: str = "India", custom_prompt: str = None) -> str:
    """Get the daily news summary prompt."""
    current_date = datetime.now().strftime("%A, %B %d, %Y")
    return get_prompt("daily_news", custom_prompt=custom_prompt, country=country, current_date=current_date)

def get_cricket_news_prompt(news_data: str = "", custom_prompt: str = None) -> str:
    """Get the cricket news prompt with optional news data."""
    current_date = datetime.now().strftime("%A, %B %d, %Y")
    return get_prompt("cricket_news", custom_prompt=custom_prompt, current_date=current_date, news_data=news_data)

def get_health_fitness_prompt(custom_prompt: str = None) -> str:
    """Get the health & fitness prompt."""
    current_date = datetime.now().strftime("%A, %B %d, %Y")
    return get_prompt("health_fitness", custom_prompt=custom_prompt, current_date=current_date)

def get_crypto_prices_prompt(custom_prompt: str = None) -> str:
    """Get the crypto prices prompt."""
    current_date = datetime.now().strftime("%A, %B %d, %Y")
    return get_prompt("crypto_prices", custom_prompt=custom_prompt, current_date=current_date)

def validate_prompts() -> dict:
    """
    Validate that all required prompts are available from all sources.

    Returns:
        dict: Status of each prompt with source information
    """
    status = {}
    json_prompts = load_prompts_from_json()

    for content_type, env_var in [
        ("daily_news", "GEMINI_PROMPT_DAILY_NEWS"),
        ("cricket_news", "GEMINI_PROMPT_CRICKET_NEWS"),
        ("health_fitness", "GEMINI_PROMPT_HEALTH_FITNESS"),
        ("crypto_prices", "GEMINI_PROMPT_CRYPTO_PRICES"),
        ("custom_content", "GEMINI_PROMPT_CUSTOM_CONTENT")
    ]:
        env_value = os.getenv(env_var)

        # Check priority order: env -> json -> default
        if env_value and env_value.strip():
            status[content_type] = "environment_variable"
        elif content_type in json_prompts and json_prompts[content_type].strip():
            status[content_type] = "json_configuration"
        elif content_type in DEFAULT_PROMPTS:
            status[content_type] = "default_fallback"
        else:
            status[content_type] = "missing"

    return status

if __name__ == "__main__":
    # Test the prompt configuration
    print("=== Prompt Configuration System Test ===")

    # Check if JSON file exists
    if os.path.exists(PROMPTS_JSON_FILE):
        print(f"✅ JSON configuration file found: {PROMPTS_JSON_FILE}")
    else:
        print(f"❌ JSON configuration file not found: {PROMPTS_JSON_FILE}")

    # Validate prompts
    status = validate_prompts()
    print("\nPrompt Status:")
    for content_type, prompt_status in status.items():
        emoji = "✅" if prompt_status != "missing" else "❌"
        print(f"  {emoji} {content_type}: {prompt_status}")

    # Test getting prompts
    print("\n=== Sample Prompts ===")

    print("\n1. Daily News Prompt (first 200 chars):")
    daily_prompt = get_daily_news_prompt("India")
    print(daily_prompt[:200] + "..." if len(daily_prompt) > 200 else daily_prompt)

    print("\n2. Cricket News Prompt (first 200 chars):")
    cricket_prompt = get_cricket_news_prompt()
    print(cricket_prompt[:200] + "..." if len(cricket_prompt) > 200 else cricket_prompt)

    print("\n3. Health & Fitness Prompt (first 200 chars):")
    health_prompt = get_health_fitness_prompt()
    print(health_prompt[:200] + "..." if len(health_prompt) > 200 else health_prompt)

    print("\n4. Crypto Prices Prompt (first 200 chars):")
    crypto_prompt = get_crypto_prices_prompt()
    print(crypto_prompt[:200] + "..." if len(crypto_prompt) > 200 else crypto_prompt)

    print("\n=== Configuration Summary ===")
    json_prompts = load_prompts_from_json()
    print(f"JSON prompts loaded: {len(json_prompts)}")
    print(f"Default prompts available: {len(DEFAULT_PROMPTS)}")
    print("✅ Prompt configuration system is working correctly!")
