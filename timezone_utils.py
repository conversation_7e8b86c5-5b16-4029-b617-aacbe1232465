import logging
import pytz
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Time zone mapping for different countries
COUNTRY_TIMEZONES = {
    "India": "Asia/Kolkata",
    "USA": "America/New_York",
    "Russia": "Europe/Moscow",
    "UK": "Europe/London",
    "Japan": "Asia/Tokyo",
    "Australia": "Australia/Sydney",
    "Germany": "Europe/Berlin",
    "Brazil": "America/Sao_Paulo",
    "Canada": "America/Toronto",
    "China": "Asia/Shanghai"
}

# Default country
DEFAULT_COUNTRY = "India"

def get_timezone_for_country(country):
    """
    Get the timezone for a specific country.

    Args:
        country (str): Country name

    Returns:
        pytz.timezone: Timezone object for the country
    """
    timezone_str = COUNTRY_TIMEZONES.get(country, COUNTRY_TIMEZONES[DEFAULT_COUNTRY])
    return pytz.timezone(timezone_str)

def get_current_time_for_country(country):
    """
    Get the current time for a specific country.

    Args:
        country (str): Country name

    Returns:
        datetime: Current time in the country's timezone
    """
    tz = get_timezone_for_country(country)
    return datetime.now(tz)

def format_time_for_country(country, time_format="%Y-%m-%d %H:%M:%S"):
    """
    Get formatted current time for a specific country.

    Args:
        country (str): Country name
        time_format (str): Time format string

    Returns:
        str: Formatted time string
    """
    current_time = get_current_time_for_country(country)
    return current_time.strftime(time_format)

def calculate_next_post_time(last_posted, interval_hours, timezone_country, initial_hour=None, initial_minute=0):
    """
    Calculate the next posting time based on interval from last post.

    Args:
        last_posted (str): ISO format datetime string of last post
        interval_hours (int): Posting interval in hours
        timezone_country (str): Country name for timezone
        initial_hour (int): Initial hour for first post (if no last_posted)
        initial_minute (int): Initial minute for first post (if no last_posted)

    Returns:
        datetime: Next posting time in the specified timezone
    """
    tz = get_timezone_for_country(timezone_country)
    current_time = datetime.now(tz)

    if not last_posted:
        # If no previous post, use initial time today or tomorrow if time has passed
        if initial_hour is not None:
            next_post = current_time.replace(hour=initial_hour, minute=initial_minute, second=0, microsecond=0)
            if next_post <= current_time:
                # If the time has passed today, schedule for tomorrow
                next_post += timedelta(days=1)
            return next_post
        else:
            # If no initial time specified, post immediately
            return current_time

    try:
        # Parse last posted time
        last_post_time = datetime.fromisoformat(last_posted)
        if last_post_time.tzinfo is None:
            # If no timezone info, assume it's in the target timezone
            last_post_time = tz.localize(last_post_time)
        else:
            # Convert to target timezone
            last_post_time = last_post_time.astimezone(tz)

        # Calculate next post time by adding interval
        next_post_time = last_post_time + timedelta(hours=interval_hours)
        return next_post_time

    except (ValueError, TypeError) as e:
        logger.warning(f"Error parsing last posted time: {e}")
        # If error, use current time
        return current_time

def is_time_to_post(last_posted, interval_hours, timezone_country, initial_hour=None, initial_minute=0, bypass_time_check=False):
    """
    Check if it's time to post based on interval scheduling.

    Args:
        last_posted (str): ISO format datetime string of last post
        interval_hours (int): Posting interval in hours
        timezone_country (str): Country name for timezone
        initial_hour (int): Initial hour for first post (if no last_posted)
        initial_minute (int): Initial minute for first post (if no last_posted)
        bypass_time_check (bool): If True, bypass the time check and always return True

    Returns:
        tuple: (should_post: bool, next_post_time: datetime, time_until_next: timedelta)
    """
    if bypass_time_check:
        logger.info("Bypassing time check - manual post or force post")
        current_time = datetime.now(get_timezone_for_country(timezone_country))
        return True, current_time, timedelta(0)

    tz = get_timezone_for_country(timezone_country)
    current_time = datetime.now(tz)

    # Calculate next posting time
    next_post_time = calculate_next_post_time(last_posted, interval_hours, timezone_country, initial_hour, initial_minute)

    # Calculate time until next post
    time_until_next = next_post_time - current_time

    # Check if it's time to post (allow 1-minute tolerance)
    should_post = time_until_next.total_seconds() <= 60

    logger.debug(f"Posting check: current={current_time.strftime('%Y-%m-%d %H:%M:%S')}, "
                f"next_post={next_post_time.strftime('%Y-%m-%d %H:%M:%S')}, "
                f"should_post={should_post}, time_until={time_until_next}")

    return should_post, next_post_time, time_until_next

def is_posting_time(scheduled_hour, country, last_posted=None, min_hours_between_posts=20, scheduled_minute=0, bypass_time_check=False):
    """
    DEPRECATED: Legacy function for backward compatibility.
    Use is_time_to_post() for new implementations.

    Check if it's time to post based on the country's timezone.

    Args:
        scheduled_hour (int): Hour of the day to post (0-23)
        country (str): Country name for timezone
        last_posted (str): ISO format datetime string of last post
        min_hours_between_posts (int): Minimum hours between posts
        scheduled_minute (int): Minute of the hour to post (0-59)
        bypass_time_check (bool): If True, bypass the time check and always return True

    Returns:
        bool: True if it's time to post, False otherwise
    """
    logger.warning("Using deprecated is_posting_time function. Consider migrating to is_time_to_post().")

    # Use the new interval-based logic
    should_post, _, _ = is_time_to_post(
        last_posted=last_posted,
        interval_hours=min_hours_between_posts,
        timezone_country=country,
        initial_hour=scheduled_hour,
        initial_minute=scheduled_minute,
        bypass_time_check=bypass_time_check
    )

    return should_post

def get_all_countries():
    """
    Get a list of all supported countries.

    Returns:
        list: List of country names
    """
    return list(COUNTRY_TIMEZONES.keys())

def get_time_difference(country1, country2):
    """
    Get the time difference between two countries in hours.

    Args:
        country1 (str): First country name
        country2 (str): Second country name

    Returns:
        float: Time difference in hours
    """
    tz1 = get_timezone_for_country(country1)
    tz2 = get_timezone_for_country(country2)

    time1 = datetime.now(tz1)
    time2 = datetime.now(tz2)

    # Calculate difference in hours
    diff_seconds = (time1.utcoffset().total_seconds() - time2.utcoffset().total_seconds())
    diff_hours = diff_seconds / 3600

    return diff_hours

def format_time_until_next_post(time_until_next):
    """
    Format a timedelta into a human-readable string.

    Args:
        time_until_next (timedelta): Time until next post

    Returns:
        str: Formatted time string
    """
    if time_until_next.total_seconds() <= 0:
        return "Now"

    total_seconds = int(time_until_next.total_seconds())
    days = total_seconds // 86400
    hours = (total_seconds % 86400) // 3600
    minutes = (total_seconds % 3600) // 60

    parts = []
    if days > 0:
        parts.append(f"{days}d")
    if hours > 0:
        parts.append(f"{hours}h")
    if minutes > 0:
        parts.append(f"{minutes}m")

    if not parts:
        return "< 1m"

    return " ".join(parts)

def get_posting_status_info(last_posted, interval_hours, timezone_country, initial_hour=None, initial_minute=0):
    """
    Get comprehensive posting status information.

    Args:
        last_posted (str): ISO format datetime string of last post
        interval_hours (int): Posting interval in hours
        timezone_country (str): Country name for timezone
        initial_hour (int): Initial hour for first post (if no last_posted)
        initial_minute (int): Initial minute for first post (if no last_posted)

    Returns:
        dict: Status information including next post time, time until next, etc.
    """
    tz = get_timezone_for_country(timezone_country)
    current_time = datetime.now(tz)

    should_post, next_post_time, time_until_next = is_time_to_post(
        last_posted, interval_hours, timezone_country, initial_hour, initial_minute
    )

    return {
        "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
        "timezone": timezone_country,
        "last_posted": last_posted,
        "interval_hours": interval_hours,
        "next_post_time": next_post_time.strftime("%Y-%m-%d %H:%M:%S"),
        "time_until_next": format_time_until_next_post(time_until_next),
        "should_post_now": should_post,
        "seconds_until_next": int(time_until_next.total_seconds())
    }
